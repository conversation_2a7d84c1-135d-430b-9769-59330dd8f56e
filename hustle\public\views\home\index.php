<?php
$pageTitle = translate('Home', $currentLanguage) . ' - ' . APP_NAME;
include __DIR__ . '/../layout/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row items-center">
            <div class="col-6">
                <div class="hero-content fade-in">
                    <h1 class="hero-title">
                        <?= translate('Empower Your Future with', $currentLanguage) ?>
                        <span class="text-gradient">Rwanda Hustle Hub</span>
                    </h1>
                    <p class="hero-description">
                        <?= translate('Join thousands of Rwandans building their careers through digital skills, freelance opportunities, and seamless payments powered by MTN Mobile Money.', $currentLanguage) ?>
                    </p>
                    <div class="hero-actions">
                        <a href="/courses" class="btn btn-primary btn-lg">
                            <i class="fas fa-graduation-cap"></i>
                            <?= translate('Explore Courses', $currentLanguage) ?>
                        </a>
                        <a href="/register" class="btn btn-secondary btn-lg">
                            <i class="fas fa-user-plus"></i>
                            <?= translate('Join Now', $currentLanguage) ?>
                        </a>
                    </div>
                    
                    <!-- Stats -->
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number counter" data-target="<?= $stats['total_users'] ?>" data-duration="2000">0</div>
                            <div class="stat-label"><?= translate('Active Users', $currentLanguage) ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number counter" data-target="<?= $stats['total_courses'] ?>" data-duration="2000">0</div>
                            <div class="stat-label"><?= translate('Courses', $currentLanguage) ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number counter" data-target="<?= $stats['total_jobs'] ?>" data-duration="2000">0</div>
                            <div class="stat-label"><?= translate('Jobs Posted', $currentLanguage) ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number counter" data-target="<?= $stats['total_services'] ?>" data-duration="2000">0</div>
                            <div class="stat-label"><?= translate('Services', $currentLanguage) ?></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="hero-image slide-in">
                    <div class="floating-cards">
                        <div class="floating-card card-1">
                            <i class="fas fa-laptop-code"></i>
                            <span><?= translate('Learn Tech Skills', $currentLanguage) ?></span>
                        </div>
                        <div class="floating-card card-2">
                            <i class="fas fa-briefcase"></i>
                            <span><?= translate('Find Jobs', $currentLanguage) ?></span>
                        </div>
                        <div class="floating-card card-3">
                            <i class="fas fa-mobile-alt"></i>
                            <span><?= translate('MTN Payments', $currentLanguage) ?></span>
                        </div>
                        <div class="floating-card card-4">
                            <i class="fas fa-certificate"></i>
                            <span><?= translate('Get Certified', $currentLanguage) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header text-center">
            <h2><?= translate('Explore Categories', $currentLanguage) ?></h2>
            <p><?= translate('Discover opportunities across various industries and skills', $currentLanguage) ?></p>
        </div>
        
        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
                <div class="category-card animate-on-scroll">
                    <div class="category-icon" style="background-color: <?= $category['color'] ?>20;">
                        <i class="<?= $category['icon'] ?>" style="color: <?= $category['color'] ?>;"></i>
                    </div>
                    <h4><?= htmlspecialchars($category['name']) ?></h4>
                    <p><?= htmlspecialchars($category['description']) ?></p>
                    <a href="/courses?category=<?= $category['id'] ?>" class="category-link">
                        <?= translate('Explore', $currentLanguage) ?>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Courses Section -->
<?php if (!empty($featuredCourses)): ?>
<section class="featured-courses-section">
    <div class="container">
        <div class="section-header">
            <h2><?= translate('Featured Courses', $currentLanguage) ?></h2>
            <a href="/courses" class="btn btn-primary">
                <?= translate('View All Courses', $currentLanguage) ?>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        <div class="courses-grid">
            <?php foreach ($featuredCourses as $course): ?>
                <div class="course-card animate-on-scroll">
                    <div class="course-image">
                        <img src="<?= $course['thumbnail'] ?? '/assets/images/course-placeholder.jpg' ?>" 
                             alt="<?= htmlspecialchars($course['title']) ?>">
                        <div class="course-level"><?= ucfirst($course['level']) ?></div>
                        <div class="course-price">
                            <?php if ($course['price'] > 0): ?>
                                <?= number_format($course['price']) ?> RWF
                            <?php else: ?>
                                <?= translate('Free', $currentLanguage) ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="course-content">
                        <div class="course-category">
                            <i class="<?= $course['category_icon'] ?>"></i>
                            <?= htmlspecialchars($course['category_name']) ?>
                        </div>
                        <h4><?= htmlspecialchars($course['title']) ?></h4>
                        <p><?= htmlspecialchars($course['short_description']) ?></p>
                        <div class="course-meta">
                            <div class="course-instructor">
                                <i class="fas fa-user"></i>
                                <?= htmlspecialchars($course['instructor_name']) ?>
                            </div>
                            <div class="course-rating">
                                <div class="stars">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star <?= $i <= $course['average_rating'] ? 'active' : '' ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <span>(<?= $course['total_reviews'] ?>)</span>
                            </div>
                        </div>
                        <div class="course-stats">
                            <span><i class="fas fa-users"></i> <?= $course['total_students'] ?> <?= translate('students', $currentLanguage) ?></span>
                            <span><i class="fas fa-clock"></i> <?= $course['duration_hours'] ?>h</span>
                        </div>
                        <a href="/courses/<?= $course['id'] ?>" class="btn btn-primary w-full">
                            <?= translate('View Course', $currentLanguage) ?>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Jobs Section -->
<?php if (!empty($featuredJobs)): ?>
<section class="featured-jobs-section">
    <div class="container">
        <div class="section-header">
            <h2><?= translate('Latest Job Opportunities', $currentLanguage) ?></h2>
            <a href="/jobs" class="btn btn-primary">
                <?= translate('View All Jobs', $currentLanguage) ?>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        
        <div class="jobs-grid">
            <?php foreach ($featuredJobs as $job): ?>
                <div class="job-card animate-on-scroll">
                    <div class="job-header">
                        <div class="job-category" style="background-color: <?= $job['category_color'] ?>20;">
                            <i class="<?= $job['category_icon'] ?>" style="color: <?= $job['category_color'] ?>;"></i>
                        </div>
                        <div class="job-meta">
                            <span class="job-type"><?= ucfirst($job['budget_type']) ?></span>
                            <?php if ($job['remote_allowed']): ?>
                                <span class="job-remote"><?= translate('Remote', $currentLanguage) ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <h4><?= htmlspecialchars($job['title']) ?></h4>
                    <p><?= htmlspecialchars(substr($job['description'], 0, 120)) ?>...</p>
                    <div class="job-details">
                        <div class="job-budget">
                            <i class="fas fa-money-bill-wave"></i>
                            <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                <?= number_format($job['budget_min']) ?> - <?= number_format($job['budget_max']) ?> RWF
                            <?php elseif ($job['budget_min']): ?>
                                <?= translate('From', $currentLanguage) ?> <?= number_format($job['budget_min']) ?> RWF
                            <?php else: ?>
                                <?= translate('Budget negotiable', $currentLanguage) ?>
                            <?php endif; ?>
                        </div>
                        <div class="job-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <?= htmlspecialchars($job['location'] ?? translate('Remote', $currentLanguage)) ?>
                        </div>
                        <div class="job-deadline">
                            <i class="fas fa-calendar"></i>
                            <?= date('M j, Y', strtotime($job['deadline'])) ?>
                        </div>
                    </div>
                    <div class="job-footer">
                        <div class="job-employer">
                            <?= translate('by', $currentLanguage) ?> <?= htmlspecialchars($job['employer_name']) ?>
                        </div>
                        <a href="/jobs/<?= $job['id'] ?>" class="btn btn-secondary btn-sm">
                            <?= translate('View Details', $currentLanguage) ?>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content text-center">
            <h2><?= translate('Ready to Start Your Journey?', $currentLanguage) ?></h2>
            <p><?= translate('Join thousands of Rwandans who are already building their future with us', $currentLanguage) ?></p>
            <div class="cta-actions">
                <a href="/register" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket"></i>
                    <?= translate('Get Started Today', $currentLanguage) ?>
                </a>
                <a href="/about" class="btn btn-secondary btn-lg">
                    <i class="fas fa-info-circle"></i>
                    <?= translate('Learn More', $currentLanguage) ?>
                </a>
            </div>
        </div>
    </div>
</section>

<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
    color: var(--white);
    padding: var(--space-3xl) 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/images/hero-pattern.svg') no-repeat center center;
    background-size: cover;
    opacity: 0.1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.text-gradient {
    background: linear-gradient(45deg, var(--primary-yellow), var(--accent-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: var(--space-2xl);
    opacity: 0.9;
    line-height: 1.6;
}

.hero-actions {
    margin-bottom: var(--space-3xl);
}

.hero-actions .btn {
    margin-right: var(--space-md);
    margin-bottom: var(--space-md);
}

.hero-stats {
    display: flex;
    gap: var(--space-xl);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    color: var(--primary-yellow);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: var(--space-xs);
}

/* Floating Cards Animation */
.hero-image {
    position: relative;
    height: 500px;
}

.floating-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: var(--white);
    color: var(--dark-gray);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

.card-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 20%;
    right: 10%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

.card-4 {
    bottom: 10%;
    right: 20%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Categories Grid */
.categories-section {
    padding: var(--space-3xl) 0;
    background: var(--light-gray);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-2xl);
}

.category-card {
    background: var(--white);
    padding: var(--space-2xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: 2rem;
}

.category-link {
    color: var(--primary-blue);
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    margin-top: var(--space-md);
    transition: all var(--transition-fast);
}

.category-link:hover {
    gap: var(--space-sm);
}

/* Courses and Jobs Grid */
.courses-grid, .jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-2xl);
}

.course-card, .job-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.course-card:hover, .job-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-teal));
    color: var(--white);
    padding: var(--space-3xl) 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: var(--space-lg);
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: var(--space-2xl);
    opacity: 0.9;
}

.cta-actions .btn {
    margin: 0 var(--space-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        justify-content: center;
        gap: var(--space-lg);
    }
    
    .floating-cards {
        display: none;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .courses-grid, .jobs-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include __DIR__ . '/../layout/footer.php'; ?>
