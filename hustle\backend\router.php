<?php
/**
 * Rwanda Hustle Hub - Router
 * Handles all HTTP requests and routes them to appropriate controllers
 */

class Router {
    private $routes = [];
    private $currentLanguage = 'en';
    
    public function __construct() {
        $this->detectLanguage();
        $this->defineRoutes();
    }
    
    private function detectLanguage() {
        // Check for language in URL, session, or browser
        $lang = $_GET['lang'] ?? $_SESSION['language'] ?? $this->getBrowserLanguage();
        
        if (in_array($lang, SUPPORTED_LANGUAGES)) {
            $this->currentLanguage = $lang;
            $_SESSION['language'] = $lang;
        }
    }
    
    private function getBrowserLanguage() {
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        $languages = explode(',', $acceptLanguage);
        
        foreach ($languages as $language) {
            $lang = substr(trim($language), 0, 2);
            if (in_array($lang, SUPPORTED_LANGUAGES)) {
                return $lang;
            }
        }
        
        return DEFAULT_LANGUAGE;
    }
    
    private function defineRoutes() {
        // Public routes
        $this->routes = [
            'GET' => [
                '/' => 'HomeController@index',
                '/home' => 'HomeController@index',
                '/about' => 'HomeController@about',
                '/contact' => 'HomeController@contact',
                
                // Authentication
                '/login' => 'AuthController@showLogin',
                '/register' => 'AuthController@showRegister',
                '/logout' => 'AuthController@logout',
                
                // Courses
                '/courses' => 'CourseController@index',
                '/courses/{id}' => 'CourseController@show',
                '/courses/{id}/lessons/{lesson_id}' => 'CourseController@showLesson',
                
                // Jobs
                '/jobs' => 'JobController@index',
                '/jobs/{id}' => 'JobController@show',
                
                // Freelance Services
                '/services' => 'FreelanceController@index',
                '/services/{id}' => 'FreelanceController@show',
                
                // User Dashboard
                '/dashboard' => 'DashboardController@index',
                '/profile' => 'UserController@profile',
                '/wallet' => 'WalletController@index',
                
                // Admin
                '/admin' => 'AdminController@index',
                '/admin/users' => 'AdminController@users',
                '/admin/courses' => 'AdminController@courses',
                '/admin/jobs' => 'AdminController@jobs',
                '/admin/analytics' => 'AdminController@analytics',
            ],
            'POST' => [
                // Authentication
                '/api/auth/login' => 'AuthController@login',
                '/api/auth/register' => 'AuthController@register',
                '/api/auth/refresh' => 'AuthController@refresh',
                
                // Courses
                '/api/courses' => 'CourseController@store',
                '/api/courses/{id}/enroll' => 'CourseController@enroll',
                '/api/courses/{id}/lessons/{lesson_id}/complete' => 'CourseController@completeLesson',
                
                // Jobs
                '/api/jobs' => 'JobController@store',
                '/api/jobs/{id}/apply' => 'JobController@apply',
                
                // Freelance Services
                '/api/services' => 'FreelanceController@store',
                '/api/services/{id}/order' => 'FreelanceController@order',
                
                // Payments
                '/api/payments/mtn' => 'PaymentController@processMTN',
                '/api/wallet/topup' => 'WalletController@topup',
                
                // File uploads
                '/api/upload/image' => 'UploadController@image',
                '/api/upload/video' => 'UploadController@video',
                '/api/upload/document' => 'UploadController@document',
            ],
            'PUT' => [
                '/api/users/{id}' => 'UserController@update',
                '/api/courses/{id}' => 'CourseController@update',
                '/api/jobs/{id}' => 'JobController@update',
                '/api/services/{id}' => 'FreelanceController@update',
            ],
            'DELETE' => [
                '/api/courses/{id}' => 'CourseController@delete',
                '/api/jobs/{id}' => 'JobController@delete',
                '/api/services/{id}' => 'FreelanceController@delete',
            ]
        ];
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove language prefix if present
        $uri = preg_replace('/^\/(' . implode('|', SUPPORTED_LANGUAGES) . ')/', '', $uri);
        
        // Remove trailing slash
        $uri = rtrim($uri, '/');
        if (empty($uri)) {
            $uri = '/';
        }
        
        // Find matching route
        $handler = $this->findRoute($method, $uri);
        
        if ($handler) {
            $this->executeHandler($handler['handler'], $handler['params']);
        } else {
            $this->handleNotFound();
        }
    }
    
    private function findRoute($method, $uri) {
        if (!isset($this->routes[$method])) {
            return null;
        }
        
        foreach ($this->routes[$method] as $pattern => $handler) {
            $params = $this->matchRoute($pattern, $uri);
            if ($params !== false) {
                return ['handler' => $handler, 'params' => $params];
            }
        }
        
        return null;
    }
    
    private function matchRoute($pattern, $uri) {
        // Convert route pattern to regex
        $regex = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $regex = '#^' . $regex . '$#';
        
        if (preg_match($regex, $uri, $matches)) {
            array_shift($matches); // Remove full match
            return $matches;
        }
        
        return false;
    }
    
    private function executeHandler($handler, $params) {
        list($controllerName, $method) = explode('@', $handler);
        
        $controllerFile = __DIR__ . "/controllers/{$controllerName}.php";
        
        if (!file_exists($controllerFile)) {
            $this->handleNotFound();
            return;
        }
        
        require_once $controllerFile;
        
        if (!class_exists($controllerName)) {
            $this->handleNotFound();
            return;
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $method)) {
            $this->handleNotFound();
            return;
        }
        
        // Set language for controller
        if (method_exists($controller, 'setLanguage')) {
            $controller->setLanguage($this->currentLanguage);
        }
        
        // Call the controller method with parameters
        call_user_func_array([$controller, $method], $params);
    }
    
    private function handleNotFound() {
        http_response_code(404);
        require_once __DIR__ . '/../public/views/errors/404.php';
    }
}
?>
