-- Rwanda Hustle Hub Database Schema
-- Comprehensive database structure for the platform

-- Create database
CREATE DATABASE IF NOT EXISTS rwanda_hustle_hub 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE rwanda_hustle_hub;

-- Users table - Core user management
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('learner', 'freelancer', 'employer', 'admin') DEFAULT 'learner',
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
    language ENUM('en', 'rw', 'fr') DEFAULT 'en',
    profile_image VARCHAR(500),
    bio TEXT,
    location VARCHAR(100),
    skills JSON,
    social_links JSON,
    email_verified_at TIMESTAMP NULL,
    phone_verified_at TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Categories table - For courses, jobs, and services
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7),
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_slug (slug),
    INDEX idx_parent_id (parent_id),
    INDEX idx_active (is_active)
);

-- Courses table - Digital learning content
CREATE TABLE courses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(500),
    thumbnail VARCHAR(500),
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'RWF',
    level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    duration_hours INT DEFAULT 0,
    language ENUM('en', 'rw', 'fr') DEFAULT 'en',
    instructor_id INT NOT NULL,
    category_id INT NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    requirements TEXT,
    what_you_learn TEXT,
    total_lessons INT DEFAULT 0,
    total_students INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    certificate_template VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_slug (slug),
    INDEX idx_instructor (instructor_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price),
    INDEX idx_rating (average_rating),
    FULLTEXT idx_search (title, description, short_description)
);

-- Course lessons table
CREATE TABLE lessons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL,
    description TEXT,
    video_url VARCHAR(500),
    video_duration INT DEFAULT 0, -- in seconds
    content TEXT,
    sort_order INT DEFAULT 0,
    is_preview BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_course_slug (course_id, slug),
    INDEX idx_course (course_id),
    INDEX idx_order (sort_order),
    INDEX idx_published (is_published)
);

-- Course quizzes table
CREATE TABLE quizzes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    lesson_id INT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    questions JSON NOT NULL, -- Array of question objects
    passing_score INT DEFAULT 70,
    time_limit INT DEFAULT 0, -- in minutes, 0 = no limit
    max_attempts INT DEFAULT 3,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_active (is_active)
);

-- Course enrollments table
CREATE TABLE enrollments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00, -- percentage
    completed_lessons JSON, -- Array of completed lesson IDs
    quiz_scores JSON, -- Quiz attempt scores
    status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
    completed_at TIMESTAMP NULL,
    certificate_url VARCHAR(500),
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_progress (progress)
);

-- Jobs table - Job postings
CREATE TABLE jobs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    budget_type ENUM('fixed', 'hourly') DEFAULT 'fixed',
    currency VARCHAR(3) DEFAULT 'RWF',
    deadline DATE,
    location VARCHAR(100),
    remote_allowed BOOLEAN DEFAULT FALSE,
    employer_id INT NOT NULL,
    category_id INT NOT NULL,
    status ENUM('draft', 'published', 'closed', 'filled') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    skills_required JSON,
    experience_level ENUM('entry', 'intermediate', 'expert') DEFAULT 'intermediate',
    total_applications INT DEFAULT 0,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_slug (slug),
    INDEX idx_employer (employer_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_budget (budget_min, budget_max),
    INDEX idx_deadline (deadline),
    FULLTEXT idx_search (title, description, requirements)
);

-- Job applications table
CREATE TABLE job_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    applicant_id INT NOT NULL,
    cover_letter TEXT,
    portfolio_url VARCHAR(500),
    proposed_budget DECIMAL(10,2),
    proposed_timeline VARCHAR(100),
    status ENUM('pending', 'reviewed', 'shortlisted', 'rejected', 'hired') DEFAULT 'pending',
    employer_notes TEXT,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (applicant_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_application (job_id, applicant_id),
    INDEX idx_job (job_id),
    INDEX idx_applicant (applicant_id),
    INDEX idx_status (status),
    INDEX idx_applied_at (applied_at)
);

-- Freelance services table
CREATE TABLE freelance_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(500),
    images JSON, -- Array of image URLs
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'RWF',
    delivery_time INT NOT NULL, -- in days
    revisions_included INT DEFAULT 1,
    freelancer_id INT NOT NULL,
    category_id INT NOT NULL,
    status ENUM('draft', 'published', 'paused', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    tags JSON,
    requirements TEXT,
    what_included TEXT,
    total_orders INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (freelancer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_slug (slug),
    INDEX idx_freelancer (freelancer_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price),
    INDEX idx_rating (average_rating),
    FULLTEXT idx_search (title, description, short_description)
);

-- Service orders table
CREATE TABLE service_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_id INT NOT NULL,
    buyer_id INT NOT NULL,
    seller_id INT NOT NULL,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    custom_requirements TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'RWF',
    delivery_date DATE,
    status ENUM('pending', 'in_progress', 'delivered', 'revision_requested', 'completed', 'cancelled', 'disputed') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    files_delivered JSON,
    buyer_rating INT NULL,
    seller_rating INT NULL,
    buyer_review TEXT,
    seller_review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (service_id) REFERENCES freelance_services(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_service (service_id),
    INDEX idx_buyer (buyer_id),
    INDEX idx_seller (seller_id),
    INDEX idx_order_number (order_number),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status)
);

-- Wallets table - User financial accounts
CREATE TABLE wallets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    balance DECIMAL(12,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'RWF',
    status ENUM('active', 'frozen', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_currency (user_id, currency),
    INDEX idx_user (user_id),
    INDEX idx_status (status)
);

-- Transactions table - All financial transactions
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    wallet_id INT NOT NULL,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'RWF',
    purpose ENUM('course_purchase', 'service_payment', 'job_payment', 'wallet_topup', 'withdrawal', 'commission', 'refund') NOT NULL,
    reference_type ENUM('course', 'service', 'job', 'topup', 'withdrawal') NULL,
    reference_id INT NULL,
    payment_method ENUM('mtn_mobile_money', 'bank_transfer', 'wallet') DEFAULT 'mtn_mobile_money',
    payment_reference VARCHAR(100),
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    description TEXT,
    metadata JSON,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_wallet (wallet_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_type (type),
    INDEX idx_purpose (purpose),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_created_at (created_at)
);

-- Reviews table - Course and service reviews
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reviewer_id INT NOT NULL,
    reviewee_id INT NOT NULL,
    reviewable_type ENUM('course', 'service', 'user') NOT NULL,
    reviewable_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    comment TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewee_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_reviewer (reviewer_id),
    INDEX idx_reviewee (reviewee_id),
    INDEX idx_reviewable (reviewable_type, reviewable_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured)
);

-- Messages table - User-to-user messaging
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id VARCHAR(100) NOT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'file', 'image') DEFAULT 'text',
    file_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_conversation (conversation_id),
    INDEX idx_sender (sender_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Notifications table - System notifications
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    category ENUM('course', 'job', 'service', 'payment', 'system', 'message') NOT NULL,
    reference_type VARCHAR(50),
    reference_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Admin logs table - System activity tracking
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip (ip_address)
);

-- Settings table - System configuration
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    key_name VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_key (key_name),
    INDEX idx_public (is_public)
);

-- File uploads table - Track uploaded files
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'video', 'document', 'audio') NOT NULL,
    purpose ENUM('profile', 'course', 'lesson', 'service', 'job', 'message', 'certificate') NOT NULL,
    reference_id INT NULL,
    cloudinary_public_id VARCHAR(255),
    cloudinary_url VARCHAR(500),
    is_processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_file_type (file_type),
    INDEX idx_purpose (purpose),
    INDEX idx_reference (reference_id),
    INDEX idx_processed (is_processed)
);

-- Insert default categories
INSERT INTO categories (name, slug, description, icon, color, sort_order) VALUES
('Technology', 'technology', 'Programming, web development, mobile apps', 'fas fa-laptop-code', '#007bff', 1),
('Design', 'design', 'Graphic design, UI/UX, branding', 'fas fa-palette', '#28a745', 2),
('Business', 'business', 'Marketing, sales, entrepreneurship', 'fas fa-briefcase', '#ffc107', 3),
('Writing', 'writing', 'Content writing, copywriting, translation', 'fas fa-pen', '#17a2b8', 4),
('Digital Marketing', 'digital-marketing', 'SEO, social media, advertising', 'fas fa-bullhorn', '#fd7e14', 5),
('Data & Analytics', 'data-analytics', 'Data analysis, statistics, research', 'fas fa-chart-bar', '#6f42c1', 6),
('Education', 'education', 'Teaching, training, curriculum development', 'fas fa-graduation-cap', '#e83e8c', 7),
('Health & Wellness', 'health-wellness', 'Fitness, nutrition, mental health', 'fas fa-heart', '#dc3545', 8);

-- Insert default settings
INSERT INTO settings (key_name, value, description, type, is_public) VALUES
('site_name', 'Rwanda Hustle Hub', 'Website name', 'string', true),
('site_description', 'Empowering Rwandan youth through digital skills and opportunities', 'Website description', 'string', true),
('default_currency', 'RWF', 'Default currency for transactions', 'string', true),
('commission_rate_freelance', '0.10', 'Commission rate for freelance services (10%)', 'number', false),
('commission_rate_jobs', '0.15', 'Commission rate for job postings (15%)', 'number', false),
('max_file_size_mb', '100', 'Maximum file upload size in MB', 'number', false),
('mtn_api_enabled', 'true', 'Enable MTN Mobile Money integration', 'boolean', false),
('email_verification_required', 'true', 'Require email verification for new users', 'boolean', false),
('course_auto_approval', 'false', 'Auto-approve new courses', 'boolean', false),
('job_auto_approval', 'true', 'Auto-approve new job postings', 'boolean', false);
