<?php
/**
 * Rwanda Hustle Hub - Main Application Entry Point
 * 
 * This file serves as the front controller for the entire application.
 * All requests are routed through this file for proper handling.
 */

// Start session
session_start();

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone for Rwanda
date_default_timezone_set('Africa/Kigali');

// Include autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

// Include configuration
require_once __DIR__ . '/../config/app.php';

// Include router
require_once __DIR__ . '/../backend/router.php';

// Handle the request
$router = new Router();
$router->handleRequest();
?>
