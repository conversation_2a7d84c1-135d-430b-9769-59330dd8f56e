# Rwanda Hustle Hub 🇷🇼

## Overview
Rwanda Hustle Hub is a comprehensive platform empowering Rwandan youth, freelancers, and small businesses through skill development, job opportunities, freelance services, and seamless MTN Mobile Money payments.

## Features
- 🎓 **Digital Courses** - Video-based learning with certificates
- 💼 **Job Board** - Connect employers with talent
- 🛠️ **Freelance Marketplace** - Services and portfolio showcase
- 💰 **MTN Mobile Money Integration** - Secure payments
- 🌍 **Multilingual Support** - Kinyarwanda, English, French
- 📱 **Mobile-First Design** - Responsive and interactive

## Tech Stack
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 8.2+
- **Database**: MySQL
- **Authentication**: JWT
- **Payments**: MTN Mobile Money API
- **File Storage**: Cloudinary

## Project Structure
```
hustle/
├── public/                 # Web-accessible files
├── backend/               # PHP backend logic
├── assets/               # CSS, JS, images
├── database/             # SQL schemas and migrations
├── config/               # Configuration files
├── uploads/              # Temporary file uploads
└── vendor/               # Composer dependencies
```

## Getting Started
1. Clone the repository
2. Install dependencies: `composer install`
3. Configure database in `config/database.php`
4. Import database schema from `database/schema.sql`
5. Start local server: `php -S localhost:8000 -t public`

## License
MIT License - Built for Rwanda's digital transformation 🚀
