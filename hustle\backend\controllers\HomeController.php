<?php
/**
 * Rwanda Hustle Hub - Home Controller
 * Handles homepage and public pages
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../utils/Translation.php';

class HomeController extends BaseController {
    
    public function index() {
        // Get featured content
        $featuredCourses = $this->getFeaturedCourses();
        $featuredJobs = $this->getFeaturedJobs();
        $featuredServices = $this->getFeaturedServices();
        $stats = $this->getPlatformStats();
        
        $data = [
            'pageTitle' => translate('Home', $this->language) . ' - ' . APP_NAME,
            'featuredCourses' => $featuredCourses,
            'featuredJobs' => $featuredJobs,
            'featuredServices' => $featuredServices,
            'stats' => $stats,
            'categories' => $this->getCategories()
        ];
        
        $this->render('home/index', $data);
    }
    
    public function about() {
        $data = [
            'pageTitle' => translate('About Us', $this->language) . ' - ' . APP_NAME,
            'stats' => $this->getPlatformStats()
        ];
        
        $this->render('home/about', $data);
    }
    
    public function contact() {
        $data = [
            'pageTitle' => translate('Contact Us', $this->language) . ' - ' . APP_NAME
        ];
        
        $this->render('home/contact', $data);
    }
    
    private function getFeaturedCourses($limit = 6) {
        $sql = "
            SELECT c.*, u.name as instructor_name, cat.name as category_name,
                   cat.icon as category_icon, cat.color as category_color
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            JOIN categories cat ON c.category_id = cat.id
            WHERE c.status = 'published' AND c.featured = 1
            ORDER BY c.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    private function getFeaturedJobs($limit = 6) {
        $sql = "
            SELECT j.*, u.name as employer_name, cat.name as category_name,
                   cat.icon as category_icon, cat.color as category_color
            FROM jobs j
            JOIN users u ON j.employer_id = u.id
            JOIN categories cat ON j.category_id = cat.id
            WHERE j.status = 'published' AND j.featured = 1
            ORDER BY j.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    private function getFeaturedServices($limit = 6) {
        $sql = "
            SELECT fs.*, u.name as freelancer_name, cat.name as category_name,
                   cat.icon as category_icon, cat.color as category_color
            FROM freelance_services fs
            JOIN users u ON fs.freelancer_id = u.id
            JOIN categories cat ON fs.category_id = cat.id
            WHERE fs.status = 'published' AND fs.featured = 1
            ORDER BY fs.created_at DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    private function getPlatformStats() {
        $stats = [];
        
        // Total users
        $stats['total_users'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM users WHERE status = 'active'"
        )['count'];
        
        // Total courses
        $stats['total_courses'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM courses WHERE status = 'published'"
        )['count'];
        
        // Total jobs
        $stats['total_jobs'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM jobs WHERE status = 'published'"
        )['count'];
        
        // Total services
        $stats['total_services'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM freelance_services WHERE status = 'published'"
        )['count'];
        
        // Total transactions
        $stats['total_transactions'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM transactions WHERE status = 'completed'"
        )['count'];
        
        // Total revenue
        $revenue = $this->db->fetch(
            "SELECT SUM(amount) as total FROM transactions WHERE type = 'credit' AND status = 'completed'"
        );
        $stats['total_revenue'] = $revenue['total'] ?? 0;
        
        return $stats;
    }
    
    private function getCategories() {
        return $this->db->fetchAll(
            "SELECT * FROM categories WHERE is_active = 1 AND parent_id IS NULL ORDER BY sort_order ASC"
        );
    }
}
?>
