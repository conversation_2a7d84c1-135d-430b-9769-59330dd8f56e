<?php
/**
 * Rwanda Hustle Hub - Course Controller
 * Handles course management and enrollment
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../utils/Translation.php';

class CourseController extends BaseController {
    
    public function index() {
        $page = max(1, (int)($this->getInput('page') ?? 1));
        $limit = 12;
        $offset = ($page - 1) * $limit;
        
        $search = $this->getInput('search');
        $category = $this->getInput('category');
        $level = $this->getInput('level');
        $priceMin = $this->getInput('price_min');
        $priceMax = $this->getInput('price_max');
        $sortBy = $this->getInput('sort') ?? 'newest';
        
        // Build query
        $whereConditions = ["c.status = 'published'"];
        $params = [];
        
        if ($search) {
            $whereConditions[] = "MATCH(c.title, c.description, c.short_description) AGAINST(? IN NATURAL LANGUAGE MODE)";
            $params[] = $search;
        }
        
        if ($category) {
            $whereConditions[] = "c.category_id = ?";
            $params[] = $category;
        }
        
        if ($level) {
            $whereConditions[] = "c.level = ?";
            $params[] = $level;
        }
        
        if ($priceMin !== null) {
            $whereConditions[] = "c.price >= ?";
            $params[] = $priceMin;
        }
        
        if ($priceMax !== null) {
            $whereConditions[] = "c.price <= ?";
            $params[] = $priceMax;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Sort options
        $sortOptions = [
            'newest' => 'c.created_at DESC',
            'oldest' => 'c.created_at ASC',
            'price_low' => 'c.price ASC',
            'price_high' => 'c.price DESC',
            'rating' => 'c.average_rating DESC',
            'popular' => 'c.total_students DESC'
        ];
        
        $orderBy = $sortOptions[$sortBy] ?? $sortOptions['newest'];
        
        // Get courses
        $sql = "
            SELECT c.*, u.name as instructor_name, cat.name as category_name,
                   cat.icon as category_icon, cat.color as category_color
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            JOIN categories cat ON c.category_id = cat.id
            WHERE {$whereClause}
            ORDER BY {$orderBy}
            LIMIT ? OFFSET ?
        ";
        
        $courses = $this->db->fetchAll($sql, array_merge($params, [$limit, $offset]));
        
        // Get total count
        $countSql = "
            SELECT COUNT(*) as total
            FROM courses c
            WHERE {$whereClause}
        ";
        
        $totalCourses = $this->db->fetch($countSql, $params)['total'];
        $totalPages = ceil($totalCourses / $limit);
        
        $data = [
            'pageTitle' => translate('Courses', $this->language) . ' - ' . APP_NAME,
            'courses' => $courses,
            'categories' => $this->getCategories(),
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $totalCourses,
                'per_page' => $limit
            ],
            'filters' => [
                'search' => $search,
                'category' => $category,
                'level' => $level,
                'price_min' => $priceMin,
                'price_max' => $priceMax,
                'sort' => $sortBy
            ]
        ];
        
        $this->render('courses/index', $data);
    }
    
    public function show($id) {
        $course = $this->getCourseDetails($id);
        
        if (!$course) {
            http_response_code(404);
            $this->render('errors/404');
            return;
        }
        
        // Check if user is enrolled
        $enrollment = null;
        if ($this->user) {
            $enrollment = $this->db->fetch(
                "SELECT * FROM enrollments WHERE user_id = ? AND course_id = ?",
                [$this->user['id'], $course['id']]
            );
        }
        
        // Get lessons
        $lessons = $this->db->fetchAll(
            "SELECT * FROM lessons WHERE course_id = ? AND is_published = 1 ORDER BY sort_order ASC",
            [$course['id']]
        );
        
        // Get reviews
        $reviews = $this->db->fetchAll(
            "SELECT r.*, u.name as reviewer_name, u.profile_image as reviewer_image
             FROM reviews r
             JOIN users u ON r.reviewer_id = u.id
             WHERE r.reviewable_type = 'course' AND r.reviewable_id = ? AND r.status = 'approved'
             ORDER BY r.created_at DESC
             LIMIT 10",
            [$course['id']]
        );
        
        // Get related courses
        $relatedCourses = $this->db->fetchAll(
            "SELECT c.*, u.name as instructor_name
             FROM courses c
             JOIN users u ON c.instructor_id = u.id
             WHERE c.category_id = ? AND c.id != ? AND c.status = 'published'
             ORDER BY c.average_rating DESC
             LIMIT 4",
            [$course['category_id'], $course['id']]
        );
        
        $data = [
            'pageTitle' => $course['title'] . ' - ' . APP_NAME,
            'course' => $course,
            'lessons' => $lessons,
            'reviews' => $reviews,
            'relatedCourses' => $relatedCourses,
            'enrollment' => $enrollment,
            'isEnrolled' => $enrollment !== null
        ];
        
        $this->render('courses/show', $data);
    }
    
    public function enroll($id) {
        $this->requireAuth();
        
        $course = $this->getCourseDetails($id);
        if (!$course) {
            $this->jsonResponse(['success' => false, 'message' => 'Course not found'], 404);
            return;
        }
        
        // Check if already enrolled
        $existingEnrollment = $this->db->fetch(
            "SELECT id FROM enrollments WHERE user_id = ? AND course_id = ?",
            [$this->user['id'], $course['id']]
        );
        
        if ($existingEnrollment) {
            $this->jsonResponse([
                'success' => false,
                'message' => translate('You are already enrolled in this course', $this->language)
            ], 400);
            return;
        }
        
        try {
            $this->db->beginTransaction();
            
            // Process payment if course is not free
            if ($course['price'] > 0) {
                $paymentResult = $this->processPayment($course['price'], 'course_purchase', $course['id']);
                if (!$paymentResult['success']) {
                    $this->db->rollback();
                    $this->jsonResponse($paymentResult, 400);
                    return;
                }
            }
            
            // Create enrollment
            $enrollmentId = $this->db->insert('enrollments', [
                'user_id' => $this->user['id'],
                'course_id' => $course['id'],
                'progress' => 0.00,
                'completed_lessons' => json_encode([]),
                'quiz_scores' => json_encode([]),
                'status' => 'active'
            ]);
            
            // Update course student count
            $this->db->query(
                "UPDATE courses SET total_students = total_students + 1 WHERE id = ?",
                [$course['id']]
            );
            
            $this->db->commit();
            
            // Log activity
            $this->logActivity('course_enrollment', [
                'course_id' => $course['id'],
                'course_title' => $course['title'],
                'price' => $course['price']
            ]);
            
            $this->jsonResponse([
                'success' => true,
                'message' => translate('Successfully enrolled in course!', $this->language),
                'redirect' => "/courses/{$course['id']}/lessons"
            ]);
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Course enrollment error: " . $e->getMessage());
            
            $this->jsonResponse([
                'success' => false,
                'message' => translate('Enrollment failed. Please try again.', $this->language)
            ], 500);
        }
    }
    
    private function getCourseDetails($id) {
        return $this->db->fetch(
            "SELECT c.*, u.name as instructor_name, u.profile_image as instructor_image,
                    u.bio as instructor_bio, cat.name as category_name,
                    cat.icon as category_icon, cat.color as category_color
             FROM courses c
             JOIN users u ON c.instructor_id = u.id
             JOIN categories cat ON c.category_id = cat.id
             WHERE c.id = ? AND c.status = 'published'",
            [$id]
        );
    }
    
    private function getCategories() {
        return $this->db->fetchAll(
            "SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC"
        );
    }
    
    private function processPayment($amount, $purpose, $referenceId) {
        // Check wallet balance
        $wallet = $this->db->fetch(
            "SELECT * FROM wallets WHERE user_id = ? AND currency = 'RWF'",
            [$this->user['id']]
        );
        
        if (!$wallet || $wallet['balance'] < $amount) {
            return [
                'success' => false,
                'message' => translate('Insufficient wallet balance. Please top up your wallet.', $this->language)
            ];
        }
        
        // Create transaction
        $transactionId = 'TXN_' . time() . '_' . $this->user['id'];
        
        $this->db->insert('transactions', [
            'user_id' => $this->user['id'],
            'wallet_id' => $wallet['id'],
            'transaction_id' => $transactionId,
            'type' => 'debit',
            'amount' => $amount,
            'currency' => 'RWF',
            'purpose' => $purpose,
            'reference_type' => 'course',
            'reference_id' => $referenceId,
            'payment_method' => 'wallet',
            'status' => 'completed',
            'description' => "Payment for course enrollment",
            'processed_at' => date('Y-m-d H:i:s')
        ]);
        
        // Update wallet balance
        $this->db->update('wallets',
            ['balance' => $wallet['balance'] - $amount],
            'id = ?',
            [$wallet['id']]
        );
        
        return ['success' => true];
    }
}
?>
