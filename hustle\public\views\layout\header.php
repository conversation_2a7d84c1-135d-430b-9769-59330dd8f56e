<!DOCTYPE html>
<html lang="<?= $currentLanguage ?? 'en' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Rwanda Hustle Hub - Empowering Rwandan youth through digital skills and opportunities">
    <meta name="keywords" content="Rwanda, skills, jobs, freelance, courses, MTN Mobile Money">
    <meta name="author" content="Rwanda Hustle Hub">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= $pageTitle ?? 'Rwanda Hustle Hub' ?>">
    <meta property="og:description" content="Empowering Rwandan youth through digital skills and opportunities">
    <meta property="og:image" content="<?= APP_URL ?>/assets/images/og-image.jpg">
    <meta property="og:url" content="<?= APP_URL ?>">
    <meta property="og:type" content="website">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link rel="stylesheet" href="/assets/css/components.css">
    <link rel="stylesheet" href="/assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <title><?= $pageTitle ?? 'Rwanda Hustle Hub' ?></title>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="row items-center justify-between">
                <div class="col">
                    <a href="/" class="navbar-brand">
                        <i class="fas fa-rocket"></i>
                        Rwanda Hustle Hub
                    </a>
                </div>
                
                <div class="col">
                    <ul class="navbar-nav justify-center">
                        <li class="nav-item">
                            <a href="/" class="nav-link <?= ($_SERVER['REQUEST_URI'] == '/' || $_SERVER['REQUEST_URI'] == '/home') ? 'active' : '' ?>">
                                <i class="fas fa-home"></i>
                                <?= translate('Home', $currentLanguage) ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/courses" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/courses') === 0 ? 'active' : '' ?>">
                                <i class="fas fa-graduation-cap"></i>
                                <?= translate('Courses', $currentLanguage) ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/jobs" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/jobs') === 0 ? 'active' : '' ?>">
                                <i class="fas fa-briefcase"></i>
                                <?= translate('Jobs', $currentLanguage) ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/services" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/services') === 0 ? 'active' : '' ?>">
                                <i class="fas fa-tools"></i>
                                <?= translate('Services', $currentLanguage) ?>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="col">
                    <div class="d-flex items-center justify-end">
                        <!-- Language Selector -->
                        <div class="language-selector">
                            <select id="languageSelect" class="form-select" onchange="changeLanguage(this.value)">
                                <option value="en" <?= $currentLanguage == 'en' ? 'selected' : '' ?>>🇺🇸 English</option>
                                <option value="rw" <?= $currentLanguage == 'rw' ? 'selected' : '' ?>>🇷🇼 Kinyarwanda</option>
                                <option value="fr" <?= $currentLanguage == 'fr' ? 'selected' : '' ?>>🇫🇷 Français</option>
                            </select>
                        </div>
                        
                        <?php if ($currentUser): ?>
                            <!-- User Menu -->
                            <div class="user-menu">
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" onclick="toggleDropdown('userDropdown')">
                                        <img src="<?= $currentUser['profile_image'] ?? '/assets/images/default-avatar.png' ?>" 
                                             alt="Profile" class="avatar-sm">
                                        <?= htmlspecialchars($currentUser['name']) ?>
                                    </button>
                                    <div id="userDropdown" class="dropdown-menu">
                                        <a href="/dashboard" class="dropdown-item">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <?= translate('Dashboard', $currentLanguage) ?>
                                        </a>
                                        <a href="/profile" class="dropdown-item">
                                            <i class="fas fa-user"></i>
                                            <?= translate('Profile', $currentLanguage) ?>
                                        </a>
                                        <a href="/wallet" class="dropdown-item">
                                            <i class="fas fa-wallet"></i>
                                            <?= translate('Wallet', $currentLanguage) ?>
                                        </a>
                                        <?php if ($currentUser['role'] == 'admin'): ?>
                                            <div class="dropdown-divider"></div>
                                            <a href="/admin" class="dropdown-item">
                                                <i class="fas fa-cog"></i>
                                                <?= translate('Admin Panel', $currentLanguage) ?>
                                            </a>
                                        <?php endif; ?>
                                        <div class="dropdown-divider"></div>
                                        <a href="/logout" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <?= translate('Logout', $currentLanguage) ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Guest Menu -->
                            <div class="auth-buttons">
                                <a href="/login" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <?= translate('Login', $currentLanguage) ?>
                                </a>
                                <a href="/register" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus"></i>
                                    <?= translate('Register', $currentLanguage) ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
