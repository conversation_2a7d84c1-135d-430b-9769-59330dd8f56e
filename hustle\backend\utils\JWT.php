<?php
/**
 * Rwanda Hustle Hub - JWT Utility Class
 * Simple JWT implementation for authentication
 */

class JWT {
    
    public static function encode($payload, $secret) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    public static function decode($jwt, $secret) {
        $parts = explode('.', $jwt);
        
        if (count($parts) !== 3) {
            throw new Exception('Invalid JWT format');
        }
        
        list($base64Header, $base64Payload, $base64Signature) = $parts;
        
        // Verify signature
        $signature = base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Signature));
        $expectedSignature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            throw new Exception('Invalid JWT signature');
        }
        
        // Decode payload
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)));
        
        if (!$payload) {
            throw new Exception('Invalid JWT payload');
        }
        
        // Check expiration
        if (isset($payload->exp) && $payload->exp < time()) {
            throw new Exception('JWT token has expired');
        }
        
        return $payload;
    }
    
    public static function createToken($userId, $role, $expiryTime = null) {
        $expiryTime = $expiryTime ?? (time() + JWT_EXPIRY);
        
        $payload = [
            'user_id' => $userId,
            'role' => $role,
            'iat' => time(),
            'exp' => $expiryTime
        ];
        
        return self::encode($payload, JWT_SECRET);
    }
    
    public static function refreshToken($token, $secret) {
        try {
            $decoded = self::decode($token, $secret);
            
            // Create new token with extended expiry
            return self::createToken($decoded->user_id, $decoded->role);
        } catch (Exception $e) {
            throw new Exception('Cannot refresh invalid token');
        }
    }
}
?>
