<?php
/**
 * Rwanda Hustle Hub - Database Migration Script
 * Run this script to create the database schema
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
$dotenv->load();

// Include configuration
require_once __DIR__ . '/../config/app.php';

function runMigration() {
    try {
        // Connect to MySQL server (without database)
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✅ Connected to MySQL server\n";
        
        // Read and execute schema
        $schema = file_get_contents(__DIR__ . '/schema.sql');
        if (!$schema) {
            throw new Exception("Could not read schema.sql file");
        }
        
        // Split into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $schema)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        echo "📝 Found " . count($statements) . " SQL statements to execute\n";
        
        foreach ($statements as $index => $statement) {
            try {
                $pdo->exec($statement);
                
                // Extract table name for better logging
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "✅ Created table: " . $matches[1] . "\n";
                } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                    echo "✅ Inserted data into: " . $matches[1] . "\n";
                } elseif (preg_match('/CREATE DATABASE/i', $statement)) {
                    echo "✅ Created database: rwanda_hustle_hub\n";
                } elseif (preg_match('/USE\s+(\w+)/i', $statement, $matches)) {
                    echo "✅ Using database: " . $matches[1] . "\n";
                }
            } catch (PDOException $e) {
                echo "❌ Error executing statement " . ($index + 1) . ": " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
                throw $e;
            }
        }
        
        echo "\n🎉 Database migration completed successfully!\n";
        echo "📊 Database: rwanda_hustle_hub\n";
        echo "🏗️  Tables created with proper indexes and relationships\n";
        echo "📦 Default categories and settings inserted\n";
        
        // Verify tables were created
        $pdo->exec("USE rwanda_hustle_hub");
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "\n📋 Created tables (" . count($tables) . "):\n";
        foreach ($tables as $table) {
            echo "   - $table\n";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "❌ Migration failed: " . $e->getMessage() . "\n";
        return false;
    }
}

function createAdminUser() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        // Check if admin user already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND role = 'admin'");
        $stmt->execute(['<EMAIL>']);
        
        if ($stmt->fetch()) {
            echo "ℹ️  Admin user already exists\n";
            return true;
        }
        
        // Create admin user
        $adminData = [
            'name' => 'Rwanda Hustle Hub Admin',
            'email' => '<EMAIL>',
            'phone' => '+250788000000',
            'password' => password_hash('admin123!@#', PASSWORD_DEFAULT),
            'role' => 'admin',
            'status' => 'active',
            'language' => 'en',
            'email_verified_at' => date('Y-m-d H:i:s'),
            'phone_verified_at' => date('Y-m-d H:i:s')
        ];
        
        $columns = implode(',', array_keys($adminData));
        $placeholders = ':' . implode(', :', array_keys($adminData));
        $sql = "INSERT INTO users ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($adminData);
        
        $adminId = $pdo->lastInsertId();
        
        // Create admin wallet
        $pdo->prepare("INSERT INTO wallets (user_id, balance, currency) VALUES (?, 0.00, 'RWF')")
            ->execute([$adminId]);
        
        echo "✅ Created admin user: <EMAIL> (password: admin123!@#)\n";
        echo "💰 Created admin wallet\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "❌ Failed to create admin user: " . $e->getMessage() . "\n";
        return false;
    }
}

// Main execution
echo "🚀 Starting Rwanda Hustle Hub Database Migration\n";
echo "================================================\n\n";

if (runMigration()) {
    echo "\n👤 Creating default admin user...\n";
    createAdminUser();
    
    echo "\n🎯 Next Steps:\n";
    echo "1. Copy .env.example to .env and configure your settings\n";
    echo "2. Install dependencies: composer install\n";
    echo "3. Start the development server: composer serve\n";
    echo "4. Visit http://localhost:8000 to see your application\n";
    echo "5. Login as admin: <EMAIL> / admin123!@#\n";
    echo "\n✨ Happy coding! 🇷🇼\n";
} else {
    echo "\n💥 Migration failed. Please check the errors above.\n";
    exit(1);
}
?>
