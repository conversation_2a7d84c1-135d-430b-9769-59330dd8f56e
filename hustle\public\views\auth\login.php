<?php
$pageTitle = translate('Login', $currentLanguage) . ' - ' . APP_NAME;
include __DIR__ . '/../layout/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="row justify-center">
            <div class="col-6">
                <div class="auth-card fade-in">
                    <div class="auth-header text-center">
                        <div class="auth-logo">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h2><?= translate('Welcome Back!', $currentLanguage) ?></h2>
                        <p><?= translate('Sign in to continue your journey', $currentLanguage) ?></p>
                    </div>
                    
                    <form id="loginForm" class="ajax-form" action="/api/auth/login" method="POST">
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i>
                                <?= translate('Email Address', $currentLanguage) ?>
                            </label>
                            <input type="email" id="email" name="email" class="form-input" 
                                   placeholder="<?= translate('Enter your email', $currentLanguage) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i>
                                <?= translate('Password', $currentLanguage) ?>
                            </label>
                            <div class="password-input">
                                <input type="password" id="password" name="password" class="form-input" 
                                       placeholder="<?= translate('Enter your password', $currentLanguage) ?>" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="remember" value="1">
                                    <span class="checkmark"></span>
                                    <?= translate('Remember me', $currentLanguage) ?>
                                </label>
                                <a href="/forgot-password" class="forgot-link">
                                    <?= translate('Forgot password?', $currentLanguage) ?>
                                </a>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-full btn-lg">
                            <i class="fas fa-sign-in-alt"></i>
                            <?= translate('Sign In', $currentLanguage) ?>
                        </button>
                    </form>
                    
                    <div class="auth-divider">
                        <span><?= translate('or', $currentLanguage) ?></span>
                    </div>
                    
                    <div class="social-login">
                        <button class="btn btn-social btn-google">
                            <i class="fab fa-google"></i>
                            <?= translate('Continue with Google', $currentLanguage) ?>
                        </button>
                    </div>
                    
                    <div class="auth-footer text-center">
                        <p>
                            <?= translate("Don't have an account?", $currentLanguage) ?>
                            <a href="/register" class="auth-link">
                                <?= translate('Sign up here', $currentLanguage) ?>
                            </a>
                        </p>
                    </div>
                </div>
                
                <!-- Features Highlight -->
                <div class="auth-features">
                    <div class="feature-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span><?= translate('Access premium courses', $currentLanguage) ?></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-briefcase"></i>
                        <span><?= translate('Apply for jobs', $currentLanguage) ?></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-wallet"></i>
                        <span><?= translate('Manage your wallet', $currentLanguage) ?></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-certificate"></i>
                        <span><?= translate('Earn certificates', $currentLanguage) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.auth-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
    display: flex;
    align-items: center;
    padding: var(--space-2xl) 0;
    position: relative;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/images/auth-pattern.svg') no-repeat center center;
    background-size: cover;
    opacity: 0.1;
}

.auth-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-3xl);
    box-shadow: var(--shadow-xl);
    max-width: 450px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.auth-header {
    margin-bottom: var(--space-2xl);
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    color: var(--white);
    font-size: 2rem;
}

.auth-header h2 {
    color: var(--dark-gray);
    margin-bottom: var(--space-sm);
}

.auth-header p {
    color: var(--medium-gray);
    margin-bottom: 0;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--medium-gray);
    cursor: pointer;
    padding: var(--space-xs);
    transition: color var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-blue);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: var(--radius-sm);
    margin-right: var(--space-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: var(--primary-blue);
    font-size: 0.875rem;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.forgot-link:hover {
    color: var(--accent-teal);
    text-decoration: underline;
}

.auth-divider {
    text-align: center;
    margin: var(--space-xl) 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: var(--white);
    padding: 0 var(--space-md);
    color: var(--medium-gray);
    font-size: 0.875rem;
}

.btn-social {
    width: 100%;
    background: var(--white);
    border: 2px solid #e9ecef;
    color: var(--dark-gray);
    margin-bottom: var(--space-md);
}

.btn-social:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.btn-google i {
    color: #db4437;
}

.auth-footer {
    margin-top: var(--space-xl);
}

.auth-footer p {
    color: var(--medium-gray);
    margin-bottom: 0;
}

.auth-link {
    color: var(--primary-blue);
    font-weight: 500;
    text-decoration: none;
}

.auth-link:hover {
    color: var(--accent-teal);
    text-decoration: underline;
}

.auth-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
    margin-top: var(--space-2xl);
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--white);
    font-size: 0.875rem;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-md);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.feature-item i {
    color: var(--primary-yellow);
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .auth-card {
        margin: var(--space-lg);
        padding: var(--space-xl);
    }
    
    .auth-features {
        grid-template-columns: 1fr;
        margin-top: var(--space-xl);
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--space-md);
        align-items: flex-start;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>
