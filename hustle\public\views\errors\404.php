<?php
$pageTitle = 'Page Not Found - Rwanda Hustle Hub';
include __DIR__ . '/../layout/header.php';
?>

<div class="error-page">
    <div class="container">
        <div class="row justify-center">
            <div class="col-8">
                <div class="error-content text-center fade-in">
                    <div class="error-illustration">
                        <i class="fas fa-search" style="font-size: 8rem; color: var(--primary-blue); opacity: 0.3;"></i>
                    </div>
                    
                    <h1 class="error-title">404</h1>
                    <h2 class="error-subtitle"><?= translate('Page Not Found', $currentLanguage) ?></h2>
                    
                    <p class="error-description">
                        <?= translate('The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.', $currentLanguage) ?>
                    </p>
                    
                    <div class="error-actions">
                        <a href="/" class="btn btn-primary btn-lg">
                            <i class="fas fa-home"></i>
                            <?= translate('Go Home', $currentLanguage) ?>
                        </a>
                        <a href="/courses" class="btn btn-secondary btn-lg">
                            <i class="fas fa-graduation-cap"></i>
                            <?= translate('Browse Courses', $currentLanguage) ?>
                        </a>
                    </div>
                    
                    <div class="helpful-links">
                        <h4><?= translate('Popular Pages', $currentLanguage) ?></h4>
                        <div class="link-grid">
                            <a href="/jobs" class="helpful-link">
                                <i class="fas fa-briefcase"></i>
                                <?= translate('Find Jobs', $currentLanguage) ?>
                            </a>
                            <a href="/services" class="helpful-link">
                                <i class="fas fa-tools"></i>
                                <?= translate('Browse Services', $currentLanguage) ?>
                            </a>
                            <a href="/contact" class="helpful-link">
                                <i class="fas fa-envelope"></i>
                                <?= translate('Contact Support', $currentLanguage) ?>
                            </a>
                            <a href="/help" class="helpful-link">
                                <i class="fas fa-question-circle"></i>
                                <?= translate('Help Center', $currentLanguage) ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: var(--space-3xl) 0;
}

.error-content {
    max-width: 600px;
    margin: 0 auto;
}

.error-illustration {
    margin-bottom: var(--space-xl);
}

.error-title {
    font-size: 6rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: var(--space-sm);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-subtitle {
    font-size: 2rem;
    color: var(--dark-gray);
    margin-bottom: var(--space-lg);
}

.error-description {
    font-size: 1.125rem;
    color: var(--medium-gray);
    margin-bottom: var(--space-2xl);
    line-height: 1.6;
}

.error-actions {
    margin-bottom: var(--space-3xl);
}

.error-actions .btn {
    margin: 0 var(--space-sm);
}

.helpful-links h4 {
    color: var(--dark-gray);
    margin-bottom: var(--space-lg);
}

.link-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
}

.helpful-link {
    display: flex;
    align-items: center;
    padding: var(--space-md);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--dark-gray);
}

.helpful-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--primary-blue);
    text-decoration: none;
}

.helpful-link i {
    margin-right: var(--space-sm);
    font-size: 1.25rem;
    color: var(--primary-blue);
}

@media (max-width: 768px) {
    .error-title {
        font-size: 4rem;
    }
    
    .error-subtitle {
        font-size: 1.5rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin: var(--space-sm) 0;
    }
    
    .link-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include __DIR__ . '/../layout/footer.php'; ?>
