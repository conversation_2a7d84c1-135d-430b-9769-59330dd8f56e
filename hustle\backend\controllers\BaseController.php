<?php
/**
 * Rwanda Hustle Hub - Base Controller
 * Provides common functionality for all controllers
 */

abstract class BaseController {
    protected $language = 'en';
    protected $user = null;
    protected $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadUser();
    }
    
    public function setLanguage($language) {
        $this->language = $language;
    }
    
    protected function loadUser() {
        $token = $this->getBearerToken();
        if ($token) {
            $this->user = $this->validateToken($token);
        }
    }
    
    protected function getBearerToken() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    protected function validateToken($token) {
        try {
            require_once __DIR__ . '/../utils/JWT.php';
            $decoded = JWT::decode($token, JWT_SECRET);
            
            // Get user from database
            $user = $this->db->fetch(
                "SELECT * FROM users WHERE id = ? AND status = 'active'",
                [$decoded->user_id]
            );
            
            return $user;
        } catch (Exception $e) {
            return null;
        }
    }
    
    protected function requireAuth() {
        if (!$this->user) {
            $this->jsonResponse(['error' => 'Authentication required'], 401);
            exit();
        }
    }
    
    protected function requireRole($roles) {
        $this->requireAuth();
        
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        
        if (!in_array($this->user['role'], $roles)) {
            $this->jsonResponse(['error' => 'Insufficient permissions'], 403);
            exit();
        }
    }
    
    protected function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    protected function render($view, $data = []) {
        // Extract data for use in view
        extract($data);
        
        // Set common variables
        $currentUser = $this->user;
        $currentLanguage = $this->language;
        $appName = APP_NAME;
        
        // Include the view file
        $viewFile = __DIR__ . "/../../public/views/{$view}.php";
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            $this->handleViewNotFound($view);
        }
    }
    
    protected function handleViewNotFound($view) {
        http_response_code(404);
        echo "View not found: {$view}";
    }
    
    protected function redirect($url, $statusCode = 302) {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit();
    }
    
    protected function getInput($key = null, $default = null) {
        $input = json_decode(file_get_contents('php://input'), true) ?? [];
        $input = array_merge($_POST, $_GET, $input);
        
        if ($key === null) {
            return $input;
        }
        
        return $input[$key] ?? $default;
    }
    
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);
            
            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field] = $error;
                    break;
                }
            }
        }
        
        return $errors;
    }
    
    private function validateField($field, $value, $rule) {
        switch ($rule) {
            case 'required':
                if (empty($value)) {
                    return "{$field} is required";
                }
                break;
                
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "{$field} must be a valid email";
                }
                break;
                
            case 'numeric':
                if (!is_numeric($value)) {
                    return "{$field} must be numeric";
                }
                break;
                
            default:
                if (strpos($rule, 'min:') === 0) {
                    $min = (int)substr($rule, 4);
                    if (strlen($value) < $min) {
                        return "{$field} must be at least {$min} characters";
                    }
                } elseif (strpos($rule, 'max:') === 0) {
                    $max = (int)substr($rule, 4);
                    if (strlen($value) > $max) {
                        return "{$field} must not exceed {$max} characters";
                    }
                }
                break;
        }
        
        return null;
    }
    
    protected function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    protected function logActivity($action, $details = []) {
        if ($this->user) {
            $this->db->insert('admin_logs', [
                'user_id' => $this->user['id'],
                'action' => $action,
                'details' => json_encode($details),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}
?>
