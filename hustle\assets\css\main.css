/**
 * Rwanda Hustle Hub - Main CSS Framework
 * Interactive, responsive design system inspired by modern social media platforms
 */

/* CSS Variables for Rwanda-inspired color scheme */
:root {
  /* Primary Colors - Rwanda Flag Inspired */
  --primary-blue: #00A1DE;
  --primary-yellow: #FFCD00;
  --primary-green: #00A651;
  --primary-red: #E31E24;
  
  /* Extended Palette */
  --accent-orange: #FF6B35;
  --accent-purple: #7B68EE;
  --accent-teal: #20B2AA;
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --medium-gray: #6C757D;
  --dark-gray: #343A40;
  --black: #000000;
  
  /* Semantic Colors */
  --success: var(--primary-green);
  --warning: var(--primary-yellow);
  --error: var(--primary-red);
  --info: var(--primary-blue);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-secondary: 'Poppins', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--dark-gray);
  background-color: var(--light-gray);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-md);
  color: var(--dark-gray);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin-bottom: var(--space-md);
}

a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--accent-teal);
  text-decoration: underline;
}

/* Interactive Elements */
button, .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
  text-decoration: none;
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--primary-green), #28a745);
  color: var(--white);
}

.btn-warning {
  background: linear-gradient(135deg, var(--primary-yellow), #ffc107);
  color: var(--dark-gray);
}

.btn-danger {
  background: linear-gradient(135deg, var(--primary-red), #dc3545);
  color: var(--white);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
}

/* Button States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:active {
  transform: translateY(0);
}

/* Ripple Effect for Buttons */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: 500;
  color: var(--dark-gray);
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid #e9ecef;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  background: var(--white);
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 161, 222, 0.1);
  transform: translateY(-1px);
}

.form-input:invalid {
  border-color: var(--error);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Card Components */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, var(--light-gray), var(--white));
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid #e9ecef;
  background: var(--light-gray);
}

/* Grid System */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--space-md));
}

.col {
  flex: 1;
  padding: 0 var(--space-md);
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Navigation */
.navbar {
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-teal));
  padding: var(--space-md) 0;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-brand {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--white) !important;
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
}

.nav-item {
  margin: 0 var(--space-sm);
}

.nav-link {
  color: var(--white) !important;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  text-decoration: none;
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Utility Classes */
.fade-in { animation: fadeIn 0.6s ease-out; }
.slide-in { animation: slideIn 0.5s ease-out; }
.pulse { animation: pulse 2s infinite; }
.bounce { animation: bounce 1s; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-3 { margin: var(--space-md); }
.m-4 { margin: var(--space-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-3 { padding: var(--space-md); }
.p-4 { padding: var(--space-lg); }
