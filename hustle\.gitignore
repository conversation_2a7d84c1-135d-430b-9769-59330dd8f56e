# Dependencies
/vendor/
/node_modules/

# Environment files
.env
.env.local
.env.production

# Logs
/logs/*.log
*.log

# Uploads
/uploads/*
!/uploads/.gitkeep

# Cache
/cache/
/tmp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Composer
composer.phar
composer.lock

# NPM
package-lock.json
yarn.lock

# Build files
/dist/
/build/

# Database
*.sqlite
*.db

# Backup files
*.bak
*.backup

# Test coverage
/coverage/

# PHP specific
*.php~
.phpunit.result.cache
