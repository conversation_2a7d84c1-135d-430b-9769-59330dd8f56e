/**
 * Rwanda Hustle Hub - Main JavaScript
 * Interactive functionality and utilities
 */

// Global app object
window.RwandaHustleHub = {
    config: {
        apiUrl: '/api',
        language: 'en',
        user: null
    },
    
    // Initialize the application
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadUserData();
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle clicks
        document.addEventListener('click', this.handleClick.bind(this));
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Handle scroll
        window.addEventListener('scroll', this.handleScroll.bind(this));
    },
    
    // Initialize interactive components
    initializeComponents: function() {
        this.initDropdowns();
        this.initModals();
        this.initTooltips();
        this.initAnimations();
    },
    
    // Handle form submissions
    handleFormSubmit: function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            this.submitAjaxForm(form);
        }
    },
    
    // Handle clicks
    handleClick: function(e) {
        const target = e.target;
        
        // Dropdown toggles
        if (target.classList.contains('dropdown-toggle')) {
            e.preventDefault();
            const dropdownId = target.getAttribute('onclick')?.match(/toggleDropdown\('(.+)'\)/)?.[1];
            if (dropdownId) {
                this.toggleDropdown(dropdownId);
            }
        }
        
        // Modal triggers
        if (target.hasAttribute('data-modal')) {
            e.preventDefault();
            this.openModal(target.getAttribute('data-modal'));
        }
        
        // Close modal
        if (target.classList.contains('modal-close') || target.classList.contains('modal-backdrop')) {
            this.closeModal();
        }
        
        // Tab navigation
        if (target.classList.contains('tab-link')) {
            e.preventDefault();
            this.switchTab(target);
        }
    },
    
    // Handle window resize
    handleResize: function() {
        this.closeAllDropdowns();
    },
    
    // Handle scroll
    handleScroll: function() {
        this.updateScrollProgress();
        this.handleStickyElements();
    },
    
    // Dropdown functionality
    initDropdowns: function() {
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        });
    },
    
    toggleDropdown: function(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        if (!dropdown) return;
        
        // Close other dropdowns
        this.closeAllDropdowns(dropdownId);
        
        // Toggle current dropdown
        dropdown.classList.toggle('show');
        
        // Position dropdown
        this.positionDropdown(dropdown);
    },
    
    closeAllDropdowns: function(except = null) {
        const dropdowns = document.querySelectorAll('.dropdown-menu');
        dropdowns.forEach(dropdown => {
            if (dropdown.id !== except) {
                dropdown.classList.remove('show');
            }
        });
    },
    
    positionDropdown: function(dropdown) {
        const rect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        if (rect.bottom > viewportHeight) {
            dropdown.classList.add('dropdown-up');
        } else {
            dropdown.classList.remove('dropdown-up');
        }
    },
    
    // Modal functionality
    initModals: function() {
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    },
    
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        
        // Focus first input
        const firstInput = modal.querySelector('input, textarea, select');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    },
    
    closeModal: function() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
        });
        document.body.classList.remove('modal-open');
    },
    
    // Tooltip functionality
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            this.createTooltip(element);
        });
    },
    
    createTooltip: function(element) {
        const tooltipText = element.getAttribute('data-tooltip');
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;
        
        element.addEventListener('mouseenter', () => {
            document.body.appendChild(tooltip);
            this.positionTooltip(element, tooltip);
            tooltip.classList.add('show');
        });
        
        element.addEventListener('mouseleave', () => {
            tooltip.remove();
        });
    },
    
    positionTooltip: function(element, tooltip) {
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        tooltip.style.left = rect.left + (rect.width - tooltipRect.width) / 2 + 'px';
        tooltip.style.top = rect.top - tooltipRect.height - 8 + 'px';
    },
    
    // Animation functionality
    initAnimations: function() {
        this.observeElements();
        this.initCounters();
    },
    
    observeElements: function() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });
        
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        animatedElements.forEach(el => observer.observe(el));
    },
    
    initCounters: function() {
        const counters = document.querySelectorAll('.counter');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = parseInt(counter.getAttribute('data-duration')) || 2000;
            this.animateCounter(counter, target, duration);
        });
    },
    
    animateCounter: function(element, target, duration) {
        let start = 0;
        const increment = target / (duration / 16);
        
        const timer = setInterval(() => {
            start += increment;
            element.textContent = Math.floor(start);
            
            if (start >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    },
    
    // AJAX functionality
    submitAjaxForm: function(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.href;
        const method = form.method || 'POST';
        
        this.showLoading();
        
        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.hideLoading();
            this.handleAjaxResponse(data, form);
        })
        .catch(error => {
            this.hideLoading();
            this.showToast('An error occurred. Please try again.', 'error');
            console.error('Ajax error:', error);
        });
    },
    
    handleAjaxResponse: function(data, form) {
        if (data.success) {
            this.showToast(data.message || 'Success!', 'success');
            
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            }
            
            if (data.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            this.showToast(data.message || 'An error occurred.', 'error');
            
            // Show field errors
            if (data.errors) {
                this.showFieldErrors(data.errors, form);
            }
        }
    },
    
    showFieldErrors: function(errors, form) {
        // Clear previous errors
        form.querySelectorAll('.field-error').forEach(error => error.remove());
        form.querySelectorAll('.form-input.error').forEach(input => input.classList.remove('error'));
        
        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('error');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    },
    
    // Loading functionality
    showLoading: function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('d-none');
        }
    },
    
    hideLoading: function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    },
    
    // Toast notifications
    showToast: function(message, type = 'info', duration = 5000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(toast);
        
        // Animate in
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    },
    
    getToastIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // Utility functions
    updateScrollProgress: function() {
        const scrolled = window.pageYOffset;
        const maxHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrolled / maxHeight) * 100;
        
        const progressBar = document.querySelector('.scroll-progress');
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }
    },
    
    handleStickyElements: function() {
        const stickyElements = document.querySelectorAll('.sticky-on-scroll');
        const scrolled = window.pageYOffset;
        
        stickyElements.forEach(element => {
            if (scrolled > 100) {
                element.classList.add('sticky');
            } else {
                element.classList.remove('sticky');
            }
        });
    },
    
    loadUserData: function() {
        // Load user data from localStorage or API
        const userData = localStorage.getItem('user');
        if (userData) {
            this.config.user = JSON.parse(userData);
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.RwandaHustleHub.init();
});

// Global utility functions
function initializeComponents() {
    window.RwandaHustleHub.initializeComponents();
}

function toggleDropdown(dropdownId) {
    window.RwandaHustleHub.toggleDropdown(dropdownId);
}
