<?php
/**
 * Rwanda Hustle Hub - Authentication Controller
 * Handles user registration, login, and authentication
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../utils/Translation.php';

class AuthController extends BaseController {
    
    public function showLogin() {
        // Redirect if already logged in
        if ($this->user) {
            $this->redirect('/dashboard');
            return;
        }
        
        $data = [
            'pageTitle' => translate('Login', $this->language) . ' - ' . APP_NAME
        ];
        
        $this->render('auth/login', $data);
    }
    
    public function showRegister() {
        // Redirect if already logged in
        if ($this->user) {
            $this->redirect('/dashboard');
            return;
        }
        
        $data = [
            'pageTitle' => translate('Register', $this->language) . ' - ' . APP_NAME,
            'roles' => $this->getAvailableRoles()
        ];
        
        $this->render('auth/register', $data);
    }
    
    public function login() {
        $input = $this->getInput();
        
        // Validate input
        $errors = $this->validate($input, [
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);
        
        if (!empty($errors)) {
            $this->jsonResponse(['success' => false, 'errors' => $errors], 400);
            return;
        }
        
        // Find user
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND status IN ('active', 'pending')",
            [$input['email']]
        );
        
        if (!$user || !password_verify($input['password'], $user['password'])) {
            $this->jsonResponse([
                'success' => false,
                'message' => translate('Invalid email or password', $this->language)
            ], 401);
            return;
        }
        
        // Check if account is active
        if ($user['status'] !== 'active') {
            $this->jsonResponse([
                'success' => false,
                'message' => translate('Account is not active. Please verify your email.', $this->language)
            ], 401);
            return;
        }
        
        // Generate JWT token
        $token = JWT::createToken($user['id'], $user['role']);
        
        // Update last login
        $this->db->update('users', 
            ['last_login_at' => date('Y-m-d H:i:s')],
            'id = ?',
            [$user['id']]
        );
        
        // Log activity
        $this->logActivity('user_login', ['user_id' => $user['id']]);
        
        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        
        $this->jsonResponse([
            'success' => true,
            'message' => translate('Login successful', $this->language),
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'profile_image' => $user['profile_image']
            ],
            'redirect' => '/dashboard'
        ]);
    }
    
    public function register() {
        $input = $this->getInput();
        
        // Validate input
        $errors = $this->validate($input, [
            'name' => 'required|min:2|max:100',
            'email' => 'required|email',
            'phone' => 'required|min:10',
            'password' => 'required|min:6',
            'confirm_password' => 'required',
            'role' => 'required'
        ]);
        
        // Check password confirmation
        if ($input['password'] !== $input['confirm_password']) {
            $errors['confirm_password'] = translate('Passwords do not match', $this->language);
        }
        
        // Check if email exists
        $existingUser = $this->db->fetch(
            "SELECT id FROM users WHERE email = ?",
            [$input['email']]
        );
        
        if ($existingUser) {
            $errors['email'] = translate('Email already exists', $this->language);
        }
        
        // Check if phone exists
        if (!empty($input['phone'])) {
            $existingPhone = $this->db->fetch(
                "SELECT id FROM users WHERE phone = ?",
                [$input['phone']]
            );
            
            if ($existingPhone) {
                $errors['phone'] = translate('Phone number already exists', $this->language);
            }
        }
        
        // Validate role
        $availableRoles = array_keys($this->getAvailableRoles());
        if (!in_array($input['role'], $availableRoles)) {
            $errors['role'] = translate('Invalid role selected', $this->language);
        }
        
        if (!empty($errors)) {
            $this->jsonResponse(['success' => false, 'errors' => $errors], 400);
            return;
        }
        
        try {
            $this->db->beginTransaction();
            
            // Create user
            $userData = [
                'name' => $this->sanitize($input['name']),
                'email' => strtolower(trim($input['email'])),
                'phone' => $this->sanitize($input['phone']),
                'password' => password_hash($input['password'], PASSWORD_DEFAULT),
                'role' => $input['role'],
                'language' => $this->language,
                'status' => 'pending' // Require email verification
            ];
            
            $userId = $this->db->insert('users', $userData);
            
            // Create wallet for user
            $this->db->insert('wallets', [
                'user_id' => $userId,
                'balance' => 0.00,
                'currency' => 'RWF'
            ]);
            
            // Send verification email (implement later)
            // $this->sendVerificationEmail($userId, $userData['email']);
            
            $this->db->commit();
            
            // Log activity
            $this->logActivity('user_register', ['user_id' => $userId, 'role' => $input['role']]);
            
            $this->jsonResponse([
                'success' => true,
                'message' => translate('Registration successful! Please check your email to verify your account.', $this->language),
                'redirect' => '/login'
            ]);
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Registration error: " . $e->getMessage());
            
            $this->jsonResponse([
                'success' => false,
                'message' => translate('Registration failed. Please try again.', $this->language)
            ], 500);
        }
    }
    
    public function logout() {
        // Clear session
        session_destroy();
        
        // Log activity
        if ($this->user) {
            $this->logActivity('user_logout', ['user_id' => $this->user['id']]);
        }
        
        $this->redirect('/');
    }
    
    public function refresh() {
        $this->requireAuth();
        
        // Generate new token
        $token = JWT::createToken($this->user['id'], $this->user['role']);
        
        $this->jsonResponse([
            'success' => true,
            'token' => $token
        ]);
    }
    
    private function getAvailableRoles() {
        return [
            'learner' => translate('Learner', $this->language),
            'freelancer' => translate('Freelancer', $this->language),
            'employer' => translate('Employer', $this->language)
        ];
    }
    
    private function sendVerificationEmail($userId, $email) {
        // TODO: Implement email verification
        // Generate verification token and send email
    }
}
?>
