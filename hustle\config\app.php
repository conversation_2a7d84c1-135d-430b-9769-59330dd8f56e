<?php
/**
 * Rwanda Hustle Hub - Application Configuration
 */

// Application constants
define('APP_NAME', $_ENV['APP_NAME'] ?? 'Rwanda Hustle Hub');
define('APP_ENV', $_ENV['APP_ENV'] ?? 'development');
define('APP_DEBUG', filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN));
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost:8000');

// Database configuration
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_PORT', $_ENV['DB_PORT'] ?? 3306);
define('DB_NAME', $_ENV['DB_NAME'] ?? 'rwanda_hustle_hub');
define('DB_USERNAME', $_ENV['DB_USERNAME'] ?? 'root');
define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');

// JWT configuration
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? 'default-secret-key');
define('JWT_EXPIRY', $_ENV['JWT_EXPIRY'] ?? 86400); // 24 hours

// MTN Mobile Money configuration
define('MTN_API_URL', $_ENV['MTN_API_URL'] ?? 'https://sandbox.momodeveloper.mtn.com');
define('MTN_API_KEY', $_ENV['MTN_API_KEY'] ?? '');
define('MTN_API_SECRET', $_ENV['MTN_API_SECRET'] ?? '');
define('MTN_SUBSCRIPTION_KEY', $_ENV['MTN_SUBSCRIPTION_KEY'] ?? '');

// Cloudinary configuration
define('CLOUDINARY_CLOUD_NAME', $_ENV['CLOUDINARY_CLOUD_NAME'] ?? '');
define('CLOUDINARY_API_KEY', $_ENV['CLOUDINARY_API_KEY'] ?? '');
define('CLOUDINARY_API_SECRET', $_ENV['CLOUDINARY_API_SECRET'] ?? '');

// File upload limits
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB for videos
define('MAX_IMAGE_SIZE', 2 * 1024 * 1024); // 2MB for images
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
define('ALLOWED_VIDEO_TYPES', ['video/mp4', 'video/webm', 'video/ogg']);
define('ALLOWED_DOCUMENT_TYPES', ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

// Security settings
define('CORS_ALLOWED_ORIGINS', explode(',', $_ENV['CORS_ALLOWED_ORIGINS'] ?? 'http://localhost:8000'));
define('RATE_LIMIT_REQUESTS', $_ENV['RATE_LIMIT_REQUESTS'] ?? 100);
define('RATE_LIMIT_WINDOW', $_ENV['RATE_LIMIT_WINDOW'] ?? 3600);

// Supported languages
define('SUPPORTED_LANGUAGES', ['en', 'rw', 'fr']);
define('DEFAULT_LANGUAGE', 'en');

// User roles
define('USER_ROLES', [
    'learner' => 1,
    'freelancer' => 2,
    'employer' => 3,
    'admin' => 4
]);

// Course pricing (in RWF)
define('COURSE_PRICE_MIN', 1000);
define('COURSE_PRICE_MAX', 15000);

// Commission rates
define('FREELANCE_COMMISSION_RATE', 0.10); // 10%
define('JOB_COMMISSION_RATE', 0.15); // 15%

// Error handling
if (APP_DEBUG) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// Set headers for security
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// CORS headers
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, CORS_ALLOWED_ORIGINS)) {
    header("Access-Control-Allow-Origin: $origin");
}
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}
?>
