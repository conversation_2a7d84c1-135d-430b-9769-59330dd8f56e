# Rwanda Hustle Hub Environment Configuration

# Application
APP_NAME="Rwanda Hustle Hub"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=rwanda_hustle_hub
DB_USERNAME=root
DB_PASSWORD=

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRY=86400

# MTN Mobile Money API
MTN_API_URL=https://sandbox.momodeveloper.mtn.com
MTN_API_KEY=your-mtn-api-key
MTN_API_SECRET=your-mtn-api-secret
MTN_SUBSCRIPTION_KEY=your-mtn-subscription-key

# Cloudinary (File Storage)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Rwanda Hustle Hub"

# Security
CORS_ALLOWED_ORIGINS=http://localhost:8000,https://rwhustlehub.rw
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log
